import { Button, useToast } from '@onyma-ds/react';
import { Form<PERSON><PERSON>ider, SubmitHandler, useForm } from 'react-hook-form';
import { NewAgendaSchema, NewAgendaType } from './validations';

import { useApi } from '@/contexts/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { FirstStep } from './FirstStep';
import { SecondStep } from './SecondStep';
import * as SC from './styles';
import { Schedule, formatSchedule, scheduleArray } from './utils';
import { LabelAndValue } from '@/@types/LabelAndValue';

type NewAgendaFormProps = {
  onClose: () => void;
};

export function NewAgendaForm({ onClose }: NewAgendaFormProps) {
  const [schedule, setSchedule] = useState<Schedule[]>(scheduleArray);
  const [scheduleErrors, setScheduleErrors] = useState<string[]>([]);
  const [selectedCompromissos, setSelectedCompromissos] = useState<
    LabelAndValue[]
  >([]);
  const [step, setStep] = useState(1);
  const queryClient = useQueryClient();
  const toast = useToast();
  const {
    calendar: { remoteCreateAgenda },
  } = useApi();

  const methods = useForm<NewAgendaType>({
    resolver: zodResolver(NewAgendaSchema),
  });

  const { mutate: createAgendaMutation } = useMutation({
    mutationFn: ({
      data,
      availability,
    }: {
      data: NewAgendaType;
      availability: { [key: string]: string[] };
    }) =>
      remoteCreateAgenda({
        nome: data.nome,
        codigo: data.codigo,
        email: data.email,
        telefone: data.telefone,
        endereco: data.endereco,
        disponibilidade: availability,
        tipoCompromisso: selectedCompromissos.map(
          (compromisso) => compromisso.value,
        ),
        tipoExame: data.exame.value,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['allCalendars'] });
      toast.addToast({
        type: 'success',
        title: 'Agenda criada com sucesso',
        description: 'a agenda foi criada com sucesso',
      });
      onClose();
    },
    onError: () => {
      toast.addToast({
        type: 'error',
        title: 'Erro ao criar agenda',
        description: 'Ocorreu um erro ao criar a agenda',
      });
    },
  });

  const handleSubmitForm: SubmitHandler<NewAgendaType> = (data) => {
    const availability = formatSchedule(schedule);

    if (step === 1 && !methods.formState.errors) {
      return;
    }

    if (step === 2) {
      const cleanedErrorsArray = scheduleErrors.filter((error) => error !== '');
      if (cleanedErrorsArray.length) return;
      createAgendaMutation({ data, availability });
      return;
    }
    setStep(2);
  };

  return (
    <>
      <SC.Container onSubmit={methods.handleSubmit(handleSubmitForm)}>
        <FormProvider {...methods}>
          {step === 1 && (
            <FirstStep
              selectedCompromissos={selectedCompromissos}
              setSelectedCompromissos={setSelectedCompromissos}
            />
          )}
          {step === 2 && (
            <SecondStep
              schedule={schedule}
              setSchedule={setSchedule}
              scheduleErrors={scheduleErrors}
              setScheduleErrors={setScheduleErrors}
            />
          )}
          <SC.WrapperButtons>
            {step === 2 && (
              <Button
                buttonType="secondary"
                variant="secondary"
                onClick={() => setStep(1)}
                type="button"
              >
                Voltar
              </Button>
            )}
            <Button
              type="submit"
              variant="secondary"
              color="white"
            >
              Continuar
            </Button>
          </SC.WrapperButtons>
        </FormProvider>
      </SC.Container>
    </>
  );
}
