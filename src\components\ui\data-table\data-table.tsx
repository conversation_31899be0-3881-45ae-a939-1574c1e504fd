import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { Search as SearchIcon } from 'lucide-react';
import { DataTablePagination } from './pagination';
import { DataTableSearch } from './search';
import { SortableHeader } from './sortable-header';
import { ColumnDef, DataTableProps } from './types';
import { useClientTable } from './use-client-table';

// Loading skeleton component
function TableSkeleton({
  columns,
  rows = 5,
}: {
  columns: number;
  rows?: number;
}) {
  return (
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div
          key={rowIndex}
          className="flex space-x-4"
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton
              key={colIndex}
              className="h-4 flex-1"
            />
          ))}
        </div>
      ))}
    </div>
  );
}

// Empty state component
function TableEmpty({ message = 'No data available' }: { message?: string }) {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <SearchIcon className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-semibold mb-2">No results found</h3>
      <p className="text-muted-foreground">{message}</p>
    </div>
  );
}

// Main DataTable component
export function DataTable<T>({
  columns,
  data,
  initialPageSize = 10,
  pageSizeOptions = [10, 25, 50, 100],
  enableSearch = true,
  enableSorting = true,
  enablePagination = true,
  multiSort = false,
  searchPlaceholder = 'Search...',
  searchDebounceMs = 300,
  className,
  tableClassName,
  headerClassName,
  bodyClassName,
  loading = false,
  emptyComponent,
  ariaLabel = 'Data table',
  ariaDescription,
  onRowClick,
  rowClassName,
}: DataTableProps<T>) {
  const searchableFields = columns
    .filter((col) => col.searchable !== false && col.accessorKey)
    .map((col) => col.accessorKey as keyof T);

  const {
    data: paginatedData,
    pagination,
    sorting,
    search,
    totalPages,
    hasData,
    isEmpty,
    noResultsFromFilter,
    handlePageChange,
    handlePageSizeChange,
    handleSort,
    handleSearch,
    handleSearchClear,
  } = useClientTable({
    data,
    initialPageSize,
    pageSizeOptions,
    searchDebounceMs,
    multiSort,
    searchableFields,
  });

  // Render cell content
  const renderCell = (column: ColumnDef<T>, row: T) => {
    if (column.cell) {
      const value = column.accessorFn
        ? column.accessorFn(row)
        : column.accessorKey
          ? row[column.accessorKey]
          : null;

      return column.cell({ row, value });
    }

    const value = column.accessorFn
      ? column.accessorFn(row)
      : column.accessorKey
        ? row[column.accessorKey]
        : '';

    return value?.toString() || '';
  };

  // Get column alignment class
  const getColumnAlignClass = (align?: 'left' | 'center' | 'right') => {
    switch (align) {
      case 'center':
        return 'text-center';
      case 'right':
        return 'text-right';
      default:
        return 'text-left';
    }
  };

  return (
    <div
      className={cn('space-y-4', className)}
      role="region"
      aria-label={ariaLabel}
      aria-describedby={ariaDescription}
    >
      {/* Search */}
      {enableSearch && (
        <div className="flex items-center justify-end">
          <DataTableSearch
            value={search.query}
            onChange={handleSearch}
            onClear={handleSearchClear}
            placeholder={searchPlaceholder}
            debounceMs={searchDebounceMs}
            className="max-w-sm"
          />
        </div>
      )}

      {/* Table status for screen readers */}
      <div
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
      >
        {loading && 'Loading table data...'}
        {hasData &&
          `Table loaded with ${paginatedData.length} rows of ${pagination.total} total results`}
        {isEmpty && 'No data available'}
        {noResultsFromFilter && 'No results found for current search'}
      </div>

      {/* Table */}
      <div className="rounded-md">
        <Table
          className={tableClassName}
          role="table"
          aria-label={ariaLabel}
          aria-describedby={ariaDescription}
          aria-rowcount={pagination.total}
          aria-busy={loading}
        >
          <TableHeader
            className={headerClassName}
            role="rowgroup"
          >
            <TableRow role="row">
              {columns.map((column) => (
                <SortableHeader
                  key={column.id}
                  sortKey={column.id}
                  currentSort={sorting}
                  onSort={handleSort}
                  sortable={enableSorting && column.sortable !== false}
                  align={column.align}
                  className={cn(column.headerClassName)}
                >
                  {column.header}
                </SortableHeader>
              ))}
            </TableRow>
          </TableHeader>

          <TableBody
            className={bodyClassName}
            role="rowgroup"
          >
            {/* Loading state */}
            {loading && !hasData && (
              <TableRow role="row">
                <TableCell
                  colSpan={columns.length}
                  className="h-24"
                  role="cell"
                  aria-label="Loading table data"
                >
                  <TableSkeleton columns={columns.length} />
                </TableCell>
              </TableRow>
            )}

            {/* Empty state */}
            {isEmpty && (
              <TableRow role="row">
                <TableCell
                  colSpan={columns.length}
                  className="h-24 "
                  role="cell"
                  aria-label="No data available"
                >
                  {emptyComponent || <TableEmpty />}
                </TableCell>
              </TableRow>
            )}

            {/* Data rows */}
            {hasData &&
              paginatedData.map((row, rowIndex) => (
                <TableRow
                  key={rowIndex}
                  role="row"
                  aria-rowindex={rowIndex + 2}
                  className={cn(
                    onRowClick &&
                      'cursor-pointer hover:bg-muted/50 focus:bg-muted/50 ',
                    rowClassName?.(row),
                  )}
                  onClick={() => onRowClick?.(row)}
                  onKeyDown={(e) => {
                    if (onRowClick && (e.key === 'Enter' || e.key === ' ')) {
                      e.preventDefault();
                      onRowClick(row);
                    }
                  }}
                  tabIndex={onRowClick ? 0 : undefined}
                  aria-label={
                    onRowClick
                      ? `Row ${rowIndex + 1}, click to select`
                      : undefined
                  }
                >
                  {columns.map((column, colIndex) => (
                    <TableCell
                      key={column.id}
                      role="cell"
                      aria-colindex={colIndex + 1}
                      className={cn(
                        getColumnAlignClass(column.align),
                        `${column.className} text-[#666666] font-roboto text-[16px]`,
                      )}
                      style={{
                        width: column.width,
                        minWidth: column.minWidth,
                        maxWidth: column.maxWidth,
                      }}
                    >
                      {renderCell(column, row)}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {enablePagination && hasData && (
        <DataTablePagination
          currentPage={pagination.page}
          totalPages={totalPages}
          pageSize={pagination.pageSize}
          total={pagination.total}
          pageSizeOptions={pageSizeOptions}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      )}
    </div>
  );
}
