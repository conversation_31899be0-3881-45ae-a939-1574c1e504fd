import { ApiResult } from '@/@types/ApiResult';
import { Menu } from '@/services/api/menus/remoteLoadMenus/types';
type Params = {
  id: string;
};

export type Result = ApiResult<MenuComplete>;

export enum MenuTypeId {
  iframe = 1,
  powerBiEmbedded = 2,
}

export type MenuComplete = {
  id: string;
  name: 'Resumo';
  description?: string;
  url?: '/EmpresaClienteResumoContrato';
  iframe: boolean;
  order: number;
  height?: number;
  ativo: boolean;
  submenu: boolean;
  module: string;
  empresas: { id: string; name: string }[];
  perfis: { id: string; name: string }[];
  menuTypeId: number | null;
  menus: Menu[];
  imagem?: string;
  icone?: string;
  menuParentId?: string;
  menuParentName?: string;
  idMenuPai?: string;
  nomeMenuPai?: string;
  agendaId: number | null;
  tipoCompromisso: {
    id: string;
    name: string;
  } | null;
  bi: null | {
    biPagina?: {
      id: number;
      biId: number;
      nome: string;
      powerBiNome: string;
    }[];
    nome: string;
    descricao: string;
    powerBiId: string;
    id: number;
  };
  biPagina: null | {
    id: number;
    biId: number;
    nome: string;
    powerBiNome: string;
  };
};

export type RemoteLoadMenu = (params: Params) => Promise<Result>;
