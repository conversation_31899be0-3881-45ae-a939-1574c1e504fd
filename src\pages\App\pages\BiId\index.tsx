import { Icons } from '@/components';
import { PageTitle } from '@/components/layouts/pageTitle';
import { useApi } from '@/contexts/api';
import { AuthStorageService } from '@/services/storage/AuthStorageService';
import { KEY_USER } from '@/services/storage/keys';
import { useToast } from '@onyma-ds/react';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { BIContainer } from './components';

export default function BiIdPage() {
  const params = useParams();
  const { addToast } = useToast();
  const { menus } = useApi();

  const [biData, setBiData] = useState<{
    name: string;
    powerBiId: string;
    powerBiNome: string;
    uuidEmpresa: string | null;
  }>({
    name: '',
    powerBiId: '',
    powerBiNome: '',
    uuidEmpresa: null,
  });

  useEffect(() => {
    const storedData = localStorage.getItem(KEY_USER);
    const parsedData = storedData ? JSON.parse(storedData) : null;
    const companyId = parsedData?.companyId || null;

    if (params.biId) {
      menus
        .loadMenu({ id: params.biId })
        .then((result) => {
          const uuidEmpresa = companyId;
          setBiData({
            powerBiId: result.result.bi?.powerBiId || '',
            powerBiNome: result.result.biPagina?.powerBiNome || '',
            name: result.result.name,
            uuidEmpresa: uuidEmpresa,
          });
        })
        .catch((error) => {
          if (
            error.response?.status === 403 &&
            error.response?.data?.code === 'TokenExpired'
          ) {
            addToast({
              type: 'error',
              title: 'Sua sessão do portal expirou',
              description:
                'Sua sessão expirou, faça login novamente para acessar esse relatório.',
              timeout: 5000,
            });
            AuthStorageService.clear();
            window.location.replace('/login');
          } else {
            addToast({
              type: 'error',
              title: error.response?.data?.title || 'Ocorreu um erro!',
              description:
                error.response?.data?.message ||
                'Um erro não especificado ocorreu, por favor entre em contato com o suporte.',
              timeout: 5000,
            });
          }
        });
    }
  }, [params, menus, addToast]);

  return (
    <div className="p-8 px-6 h-full flex flex-col gap-8 w-full">
      <PageTitle
        title={biData.name}
        icon={<Icons.RAGo.GoGraph size={24} />}
      />
      <BIContainer
        powerBiId={biData.powerBiId}
        powerBiNome={biData.powerBiNome}
        idEmpresaCliente={biData.uuidEmpresa}
      />
    </div>
  );
}
