import * as React from 'react';

import { cn } from '@/lib/utils';
import { DynamicIcon, IconName } from 'lucide-react/dynamic';
import { Label } from './label';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  errorMessage?: string;
  endIcon?: IconName;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, errorMessage, label, endIcon, ...props }, ref) => {
    return (
      <div className="flex flex-col gap-1.5 w-full">
        {label && (
          <Label
            className="font-normal"
            htmlFor={props.id}
          >
            {label}
          </Label>
        )}
        <div className="relative">
          <input
            type={type}
            data-slot="input"
            className={cn(
              'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-11 w-full min-w-0 rounded-sm border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-60 disabled:bg-muted md:text-sm',
              'focus-visible:border-ring focus-visible:ring-primary focus-visible:ring-[0.2px]',
              'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
              className,
            )}
            ref={ref}
            aria-invalid={!!errorMessage}
            {...props}
          />
          {endIcon && (
            <DynamicIcon
              name={endIcon}
              className="absolute top-1/2 right-4 transform -translate-y-1/2 h-4 w-4 opacity-50"
            />
          )}
        </div>
        {errorMessage && (
          <p className="text-xs text-red-400 font-normal">{errorMessage}</p>
        )}
      </div>
    );
  },
);

Input.displayName = 'Input';

export { Input };
