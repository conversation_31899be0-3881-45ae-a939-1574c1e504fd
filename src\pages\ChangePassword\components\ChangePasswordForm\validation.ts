import { z } from 'zod';

export const changePasswordFormSchema = z
  .object({
    newPassword: z.string().min(1, 'Esse campo é obrigatório'),
    confirmPassword: z.string().min(1, 'Esse campo é obrigatório'),
  })
  .refine(
    ({ newPassword, confirmPassword }) => newPassword === confirmPassword,
    {
      message: 'As senhas não são iguais.',
      path: ['confirmPassword'],
    },
  );

export type ChangePasswordFormValues = z.infer<typeof changePasswordFormSchema>;
