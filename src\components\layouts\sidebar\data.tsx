import {
  Chart<PERSON>ie,
  House,
  LayoutGrid,
  MessageSquareText,
  Settings2,
} from 'lucide-react';

export type MenuType = {
  name: string;
  value: string;
  url: string;
};

export const moduleOptions = [
  {
    name: '<PERSON><PERSON>cio',
    value: 'START',
    url: '/app/home',
    icon: <House className="!w-5.5 !h-5.5" />,
  },
  {
    name: 'Apps',
    value: 'APPS',
    url: '/app/apps',
    icon: <LayoutGrid className="!w-5.5 !h-5.5" />,
  },
  {
    name: 'Indicadores',
    value: 'INDICADORES',
    url: '/app/indicadores',
    icon: <ChartPie className="!w-5.5 !h-5.5" />,
  },
  {
    name: 'FAQ',
    value: 'FAQ',
    url: '/app/faq',
    icon: <MessageSquareText className="!w-5.5 !h-5.5" />,
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    value: 'SISTEMA',
    url: '/app/sistema',
    icon: <Settings2 className="!w-5.5 !h-5.5" />,
  },
];
