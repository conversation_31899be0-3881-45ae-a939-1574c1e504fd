import { EmptyPageMenu } from '@/components/layouts/cards/empty';
import { PageCard } from '@/components/layouts/cards/page-card';
import { Spinner } from '@/components/loaders/spinner';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { useCompany } from '@/contexts/company';
import { useQuery } from '@tanstack/react-query';
import { handleCardClick } from './utils';

export default function Main() {
  const { company } = useCompany();
  const { user } = useAuth();

  const {
    menus: { loadMenus },
  } = useApi();

  const {
    data: allSubMenus,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['allSubMenus', user, company],
    queryFn: () =>
      loadMenus({
        ativo: true,
        empresa: company?.id ?? user.companies[0]?.id,
        perfil: user?.currentRole.id,
        modulo: 'APPS',
      }),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  if (isLoading || isFetching) {
    return <Spinner />;
  }

  return (
    <div className="flex flex-wrap gap-8 p-8 px-6">
      {allSubMenus?.result[0] ? (
        allSubMenus?.result[0]?.menus.map((menu) => (
          <PageCard
            key={menu.title}
            title={menu.title}
            icon={menu?.icon}
            link={handleCardClick(menu)}
          />
        ))
      ) : (
        <EmptyPageMenu />
      )}
    </div>
  );
}
