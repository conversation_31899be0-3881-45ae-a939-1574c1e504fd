import { <PERSON><PERSON> } from '@/components/ui/button';
import { CircleCheckBig } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function PasswordChangedSuccessfully() {
  return (
    <div className="flex flex-col gap-10 w-[360px]">
      <div className="flex flex-col gap-4 items-center">
        <CircleCheckBig
          size={40}
          className="text-primary"
        />
        <h1 className="text-center text-2xl font-medium text-gray-700">
          Senha alterada com sucesso!
        </h1>
      </div>

      <Link to="/app/home">
        <Button
          className="w-full"
          type="button"
        >
          Acessar minha conta
        </Button>
      </Link>
    </div>
  );
}
