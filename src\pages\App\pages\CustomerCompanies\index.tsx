import { Spinner } from '@/components/loaders/spinner';
import { CustomerCompaniesTable } from '@/components/tables/customerCompanies';
import { useApi } from '@/contexts/api';
import { useQuery } from '@tanstack/react-query';

export default function CustomerCompanyPage() {
  const {
    clients: { loadClientCompanies },
  } = useApi();

  const {
    data: allCompanies,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['all-companiesss'],
    queryFn: () => loadClientCompanies(),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  if (isLoading || isFetching) {
    return <Spinner />;
  }

  return (
    <div className="flex flex-wrap gap-8 p-8 px-6">
      <CustomerCompaniesTable data={allCompanies?.result || []} />
    </div>
  );
}
