import styled from 'styled-components';

export const Container = styled.button`
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
`;

export const OrderBox = styled.div`
  color: ${({ theme }) => theme.colors.gray_70};
  display: flex;
  align-items: center;
  justify-content: center;

  & > svg {
    width: 8px;

    &[data-state='active'] {
      color: ${({ theme }) => theme.colors.black};
    }
  }
`;
