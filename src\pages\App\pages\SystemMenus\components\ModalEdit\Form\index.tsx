/* eslint-disable react-hooks/exhaustive-deps */
import { useClientCompanies } from '@/pages/App/pages/ClientCompanies/contexts/clientCompanies';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  InputBox,
  InputTextBox,
  SelectBox,
  Switch,
  useToast,
} from '@onyma-ds/react';
import { useEffect, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import ComboboxCompany from './CompaniesMultiCombobox';
import UserGroupMultiCombobox from './UserGroupMultiCombobox';
import { moduleOptions, pageTypeOptions } from './data';
import { EditMenuSchema, EditMenuType } from './validations';

import { useApi } from '@/contexts/api';
import { router } from '@/router';
import { MenuComplete } from '@/services/api/menus/remoteLoadMenu';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useMenus } from '../../../contexts/menus';
import * as SC from './styles';
import { findRecursivesMenu } from '../../utils';
import { IconAndValue, LabelAndValue } from '@/@types/LabelAndValue';
import { IconCombobox } from '@/components/ui/icon-combobox';

export interface LabelAndValueCompromissos {
  label: string;
  value: string;
  requerEmpresa?: string[];
}

export function EditMenuForm({
  onClose,
  defaultValues,
}: {
  onClose: () => void;
  defaultValues: MenuComplete | undefined;
}) {
  const toast = useToast();
  const { refetch } = useMenus();
  const { allCompanies } = useClientCompanies();
  const {
    bi: { loadBIReports, loadBIReport },
    menus: { loadMenus, updateMenu },
    userGroup: { loadUserGroups },
    calendar: { loadCalendars, remoteLoadCompromissos },
  } = useApi();
  const [allCompromissos, setAllCompromissos] = useState<LabelAndValue[]>([]);
  const [selectedCompanies, setSelectedCompanies] = useState<LabelAndValue[]>(
    [],
  );
  const [allReportPages, setAllReportPages] = useState<LabelAndValue[]>([]);
  const [selectedUsersGroup, setSelectedUsersGroup] = useState<
    LabelAndValueCompromissos[]
  >([]);
  const {
    formState: { errors },
    handleSubmit,
    control,
    watch,
    getValues,
    setError,
    setValue,
  } = useForm<EditMenuType>({
    resolver: zodResolver(EditMenuSchema),
    defaultValues: {
      nome: defaultValues?.name,
      descricao: defaultValues?.description,
      icone: {
        label: defaultValues?.icone ?? '',
        value: defaultValues?.icone ?? '',
      },
      url: defaultValues?.url,
      ordem: defaultValues?.order,
      submenu: !!defaultValues?.submenu,
      ativo: !!defaultValues?.ativo,
      companies: '',
      userGroup: '',
      agenda: {},
      compromisso: {
        label: defaultValues?.tipoCompromisso?.name ?? '',
        value: defaultValues?.tipoCompromisso?.id ?? '',
      },
      pagina: {},
      biRelatorio: {
        label: defaultValues?.biPagina?.nome ?? '',
        value: String(defaultValues?.biPagina?.id),
      },
      menuTipo: pageTypeOptions.find(
        (option) => option.value === String(defaultValues?.menuTypeId),
      ),
      modulo: {
        label: defaultValues?.module,
        value: defaultValues?.module,
      },
    },
  });

  const { data: userGroups } = useQuery({
    queryKey: ['userGroups'],
    queryFn: () => loadUserGroups(),
  });

  const { data: allReports } = useQuery({
    queryKey: ['allReports'],
    queryFn: () => loadBIReports(),
  });

  const { data: allCompromissosData } = useQuery({
    queryKey: ['allCompromissos'],
    queryFn: () => remoteLoadCompromissos(),
  });

  const { data: allCalendars } = useQuery({
    queryKey: ['allCalendars'],
    queryFn: () => loadCalendars(),
  });

  const { data: allMenus } = useQuery({
    queryKey: ['allMenus', watch('modulo')],
    queryFn: () =>
      loadMenus({ ativo: true, modulo: watch('modulo').value, menuTipo: '5' }),
  });

  const { data: report, isLoading: isLoadingReports } = useQuery({
    queryKey: ['biReport', watch('relatorio')],
    queryFn: () => loadBIReport({ id: watch('relatorio')?.value as string }),
  });

  const { mutate: editMenuMutate } = useMutation({
    mutationFn: (data: EditMenuType) => {
      const isIframe = data.menuTipo.value === '1';
      const isReport = data.menuTipo.value === '2';
      const isReadyPage = data.menuTipo.value === '3';
      const isExternalPage = data.menuTipo.value === '4';
      const isParentPage = data.menuTipo.value === '5';

      const urlToSend =
        isExternalPage || isIframe ? data.url : data.pagina?.label;

      const compromissoId =
        data.compromisso?.value !== '' ? data.compromisso?.value : null;

      return updateMenu({
        ativo: !!data.ativo,
        id: defaultValues?.id as string,
        nome: data.nome,
        icone: data.icone?.label ?? '',
        descricao: data.descricao ?? '',
        url: isReport || isParentPage ? '#' : (urlToSend as string),
        iframe: !!isIframe,
        submenu: !!data.submenu,
        modulo: String(data.modulo?.value),
        ordem: data.ordem,
        menuTipo: data.menuTipo.value,
        agendaId: isReadyPage ? Number(data.agenda?.value) : null,
        parente: data.submenu ? String(data.localizacao?.value) : null,
        tipoCompromissoId: isReadyPage
          ? (compromissoId as string | null)
          : null,
        empresas: selectedCompanies.map((company) => company.value),
        perfis: selectedUsersGroup.map((userGroup) => userGroup.value),
        biId: data.relatorio ? Number(data.relatorio.value) : null,
        biPaginaId: data.biRelatorio ? Number(data.biRelatorio.value) : null,
      });
    },
    onSuccess: async () => {
      await refetch();
      toast.addToast({
        title: 'Página editada com sucesso.',
        type: 'success',
        description: 'A página foi editada com sucesso.',
      });
    },
    onError: () => {
      toast.addToast({
        title: 'Erro ao editar página.',
        type: 'error',
        description:
          'Aconteceu algum erro ao tentar editar página. Tente novamente.',
      });
    },
  });

  const handleSubmitForm: SubmitHandler<EditMenuType> = (data) => {
    if (
      selectedUsersGroup.find((userGroup) => userGroup.requerEmpresa) &&
      selectedCompanies.length === 0
    ) {
      setError('companies', {
        message: 'Selecione uma empresa para o grupo de usuários selecionado.',
      });
      return;
    }

    editMenuMutate(data);
  };

  useEffect(() => {
    if (!defaultValues) return;
    const defaultAgenda = allCalendars?.result.find(
      (calendar) => calendar.id === defaultValues.agendaId,
    );
    setValue('agenda', {
      label: defaultAgenda?.nome ?? '',
      value: String(defaultAgenda?.id) ?? 1,
      compromissos: defaultAgenda?.tipoCompromisso ?? [],
    });
    setValue('compromisso', {
      label: defaultValues.tipoCompromisso?.name ?? '',
      value: defaultValues.tipoCompromisso?.id ?? '',
    });
  }, [allCalendars, defaultValues]);

  useEffect(() => {
    if (!defaultValues) return;
    const formattedCompanies = defaultValues.empresas.map((company) => {
      return {
        label: company.name,
        value: company.id,
      };
    });

    const formattedUserGroups = defaultValues.perfis.map((company) => {
      return {
        label: company.name,
        value: company.id,
      };
    });

    const findPage = allReadyPages?.find(
      (readyPage) => readyPage.label === defaultValues.url,
    );

    setValue('pagina', {
      label: findPage?.label ?? '',
      value: findPage?.value ?? '',
    });
    setSelectedCompanies(formattedCompanies);
    setSelectedUsersGroup(formattedUserGroups);
  }, [defaultValues]);

  useEffect(() => {
    if (!getValues('agenda')) return;

    const compromissos = getValues('agenda')?.compromissos;
    const findCompromissos = allCompromissosData?.result.filter((comp) => {
      return compromissos?.includes(comp.codigoSoc);
    });

    if (findCompromissos?.length === 0) {
      setAllCompromissos([]);
      setValue('compromisso', { label: '', value: '' });
      return;
    }

    setAllCompromissos(
      findCompromissos?.map((comp) => {
        return {
          label: comp.compromisso,
          value: comp.codigoSoc,
        };
      }) ?? [],
    );
  }, [watch('agenda')]);

  useEffect(() => {
    if (!getValues('relatorio')) return;

    setAllReportPages(
      report?.result.biPage.map((comp) => {
        return {
          label: String(comp.name),
          value: String(comp.id),
        };
      }) ?? [],
    );
  }, [report]);

  useEffect(() => {
    if (defaultValues?.ativo) {
      setValue('ativo', !!defaultValues.ativo);
    }
    if (defaultValues?.biPagina) {
      setValue('biRelatorio', {
        label: String(defaultValues.biPagina?.nome),
        value: String(defaultValues.biPagina?.id),
      });
    }
    if (defaultValues?.submenu && defaultValues.menuParentName) {
      setValue('localizacao', {
        label: String(defaultValues.menuParentName),
        value: String(defaultValues.menuParentId),
      });
    }
  }, [defaultValues]);

  const allSubmenus = allMenus?.result?.map((menus) => menus.menus).flat();

  const allAppPages = router.routes.find(
    (route) => route.path === '/app',
  )?.children;

  const allReadyPages = allAppPages?.map((page) => {
    return {
      label: String(page.path),
      value: String(page.id),
    };
  });

  return (
    <SC.Container onSubmit={handleSubmit(handleSubmitForm)}>
      <Controller
        control={control}
        name="nome"
        render={({ field }) => (
          <InputTextBox
            id="nome"
            type="text"
            placeholder="Digite o nome da página"
            label="Nome do menu (Card)"
            defaultValue={defaultValues?.name}
            error={!!errors.nome}
            feedbackText={errors.nome?.message}
            onChangeValue={field.onChange}
          />
        )}
      />

      <Controller
        control={control}
        name="descricao"
        render={({ field }) => (
          <InputTextBox
            id="descricao"
            type="text"
            defaultValue={defaultValues?.description}
            placeholder="Digite a descrição"
            label="Descrição do card"
            error={!!errors.descricao}
            feedbackText={errors.descricao?.message}
            onChangeValue={field.onChange}
          />
        )}
      />

      <Controller
        control={control}
        name="icone"
        render={({ field }) => (
          <IconCombobox
            value={field.value as IconAndValue}
            onValueChange={field.onChange}
            errorMessage={errors.icone?.message}
          />
        )}
      />

      <SC.SwitchContainer>
        <Controller
          control={control}
          name="ativo"
          render={({ field }) => (
            <Switch
              id="menu-form-isActive"
              name="ativo"
              checked={!!field.value}
              onChange={field.onChange}
            />
          )}
        />

        <Switch.Label htmlFor="menu-form-isSubmenu">Ativo?</Switch.Label>
      </SC.SwitchContainer>

      <SC.WrapperSectionModule>
        <Controller
          control={control}
          name="modulo"
          render={({ field }) => (
            <SelectBox
              label="Categoria"
              placeholder="Selecione a categoria"
              options={moduleOptions}
              optionSelected={field.value}
              onSelect={(event) => {
                setValue('localizacao', { label: '', value: '' });
                field.onChange(event);
              }}
              error={!!errors.modulo}
              feedbackText={errors.modulo?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="ordem"
          defaultValue={Number(defaultValues?.order)}
          render={({ field }) => (
            <InputTextBox
              id="ordem"
              type="number"
              defaultValue={Number(defaultValues?.order)}
              placeholder="Digite a posição"
              label="Ordem de exibição"
              min={0}
              error={!!errors.ordem}
              feedbackText={errors.ordem?.message}
              onChangeValue={field.onChange}
            />
          )}
        />
      </SC.WrapperSectionModule>

      <Controller
        control={control}
        name="menuTipo"
        render={({ field }) => (
          <SelectBox
            label="Tipo da página"
            placeholder="Selecione o tipo do menu"
            options={pageTypeOptions}
            optionSelected={field.value}
            onSelect={field.onChange}
            error={!!errors.menuTipo}
            feedbackText={errors.menuTipo?.message}
          />
        )}
      />

      {(watch('menuTipo')?.value === '1' ||
        watch('menuTipo')?.value === '4') && (
        <Controller
          control={control}
          name="url"
          render={({ field }) => (
            <InputTextBox
              id="url"
              type="text"
              placeholder="Digite a URL"
              label="Url"
              error={!!errors.url}
              value={field.value}
              feedbackText={errors.url?.message}
              onChangeValue={field.onChange}
            />
          )}
        />
      )}

      {watch('menuTipo')?.value === '2' && (
        <SC.WrapperSection>
          <Controller
            control={control}
            name="relatorio"
            defaultValue={{
              label: defaultValues?.bi?.nome as string,
              value: String(defaultValues?.bi?.id),
            }}
            render={({ field }) => (
              <SelectBox
                label="Relatório"
                placeholder="Selecione o relatório"
                options={
                  allReports?.result.map((report) => {
                    return {
                      label: report.name,
                      value: String(report.id),
                    };
                  }) ?? []
                }
                optionSelected={field.value}
                onSelect={(event) => {
                  setValue('biRelatorio', { label: '', value: '' });
                  field.onChange(event);
                }}
                error={!!errors.relatorio}
                feedbackText={errors.relatorio?.message}
              />
            )}
          />
          <Controller
            control={control}
            name="biRelatorio"
            render={({ field }) => (
              <SelectBox
                disabled={isLoadingReports}
                label="Página do relatório"
                placeholder="Selecione o relatório"
                options={allReportPages ?? []}
                optionSelected={field.value}
                onSelect={field.onChange}
                error={!!errors.biRelatorio}
                feedbackText={errors.biRelatorio?.message}
              />
            )}
          />
        </SC.WrapperSection>
      )}

      {watch('menuTipo')?.value === '3' && (
        <SC.WrapperSection>
          <Controller
            control={control}
            name="pagina"
            render={({ field }) => (
              <SelectBox
                label="Página"
                placeholder="Selecione a página"
                options={allReadyPages ?? []}
                optionSelected={field.value}
                onSelect={field.onChange}
                error={!!errors.pagina}
                feedbackText={errors.pagina?.message}
              />
            )}
          />

          <Controller
            control={control}
            name="agenda"
            render={({ field }) => (
              <SelectBox
                label="Agenda"
                placeholder="Selecione a agenda"
                options={
                  allCalendars?.result.map((calendar) => {
                    return {
                      label: calendar.nome,
                      value: String(calendar.id),
                      compromissos: calendar.tipoCompromisso,
                    };
                  }) ?? []
                }
                optionSelected={field.value}
                onSelect={field.onChange}
                error={!!errors.agenda}
                feedbackText={errors.agenda?.message}
              />
            )}
          />

          <Controller
            control={control}
            name="compromisso"
            render={({ field }) => (
              <SelectBox
                label="Compromisso"
                placeholder="Selecione o compromisso"
                options={allCompromissos}
                optionSelected={field.value}
                onSelect={field.onChange}
                error={!!errors.compromisso}
                feedbackText={errors.compromisso?.message}
              />
            )}
          />
        </SC.WrapperSection>
      )}

      <InputBox
        error={!!errors.submenu}
        feedbackText={errors.submenu?.message}
      >
        <SC.SwitchContainer>
          <Controller
            control={control}
            name="submenu"
            render={({ field }) => (
              <Switch
                id="menu-form-isSubmenu"
                name="submenu"
                checked={!!field.value}
                onChange={field.onChange}
              />
            )}
          />

          <Switch.Label htmlFor="menu-form-isSubmenu">
            É uma submenu?
          </Switch.Label>
        </SC.SwitchContainer>
      </InputBox>

      {watch('submenu') && (
        <SC.WrapperSection>
          <Controller
            control={control}
            name="localizacao"
            defaultValue={{
              label: defaultValues?.menuParentName as string,
              value: defaultValues?.menuParentId as string,
            }}
            render={({ field }) => (
              <SelectBox
                label="Selecione o menu principal"
                placeholder="Selecione o menu principal"
                options={findRecursivesMenu(
                  allSubmenus ?? [],
                  String(defaultValues?.id),
                )}
                optionSelected={field.value}
                onSelect={field.onChange}
                error={!!errors.localizacao}
                feedbackText={errors.localizacao?.message}
              />
            )}
          />
        </SC.WrapperSection>
      )}

      <SC.WrapperSection>
        <span>Permissões</span>

        <Controller
          control={control}
          name="companies"
          render={({ field }) => (
            <>
              <ComboboxCompany
                label="Empresas"
                company={
                  allCompanies?.map((company) => {
                    return {
                      label: company.name,
                      value: company.id,
                    };
                  }) ?? []
                }
                onChange={field.onChange}
                selectedCompanies={selectedCompanies}
                setSelectedCompanies={setSelectedCompanies}
              />
              {errors.companies && (
                <SC.ErrorMessage>{errors.companies.message}</SC.ErrorMessage>
              )}
            </>
          )}
        />

        <Controller
          control={control}
          name="userGroup"
          render={({ field }) => (
            <>
              <UserGroupMultiCombobox
                label="Grupos de usuários"
                company={
                  userGroups?.result.map((company) => {
                    return {
                      label: company.nome,
                      value: company.id,
                      requerEmpresa: company.requerEmpresa,
                    };
                  }) ?? []
                }
                onChange={field.onChange}
                selectedCompanies={selectedUsersGroup}
                setSelectedCompanies={setSelectedUsersGroup}
              />
              {errors.userGroup && (
                <SC.ErrorMessage>{errors.userGroup.message}</SC.ErrorMessage>
              )}
            </>
          )}
        />
      </SC.WrapperSection>

      <SC.WrapperButtons>
        <Button
          buttonType="secondary"
          variant="secondary"
          onClick={onClose}
          type="button"
        >
          Cancelar
        </Button>
        <Button
          variant="secondary"
          type="submit"
          color="white"
        >
          Editar
        </Button>
      </SC.WrapperButtons>
    </SC.Container>
  );
}
