import { ApiResult } from '@/@types/ApiResult';

type Params = {
  nome: string;
  descricao: string;
  empresas: string[];
  perfis: string[];
  iframe: boolean;
  submenu: boolean;
  ordem: number;
  menuTipo: string;
  modulo: string | null;
  url: string | null;
  agendaId: number | null;
  imagem?: string | null;
  parente: string | null;
  tipoCompromissoId: string | null;
  icone?: string | null;
  altura?: null;
  biId?: string | number | null;
  biPaginaId?: string | number | null;
};

export type Result = ApiResult<null>;

export type RemoteCreateMenu = (params: Params) => Promise<Result>;
