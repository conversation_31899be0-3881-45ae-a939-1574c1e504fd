import { ClientCompany } from '@/services/api/clients/remoteLoadClientCompanies';
import { Button } from '../ui/button';
import { ColumnDef, DataTable } from '../ui/data-table';
import { format } from 'date-fns';

type CustomerCompaniesTableProps = {
  data: ClientCompany[];
  loading?: boolean;
};

export function CustomerCompaniesTable({
  data,
  loading,
}: CustomerCompaniesTableProps) {
  const columns: ColumnDef<ClientCompany>[] = [
    {
      id: 'name',
      header: 'Nome',
      accessorKey: 'name',
      sortable: true,
      searchable: true,
      width: 320,
    },
    {
      id: 'isActive',
      header: 'Ativo',
      accessorKey: 'isActive',
      sortable: true,
      searchable: true,
      cell: ({ value }) => <span>{value ? 'Sim' : 'Não'}</span>,
    },
    {
      id: 'isIncluded',
      header: 'Incluso Ben + Saúde',
      accessorKey: 'includedBenSaude',
      sortable: true,
      searchable: true,
      cell: ({ value }) => <span>{value ? 'Sim' : 'Não'}</span>,
    },
    {
      id: 'created_at',
      header: 'Data de cadastro',
      accessorKey: 'createdAt',
      sortable: true,
      cell: ({ value }) => <span>{format(new Date(value), 'dd/MM/yyyy')}</span>,
    },
    {
      id: 'actions',
      header: 'Actions',
      sortable: false,
      cell: () => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
          >
            Edit
          </Button>
          <Button
            size="sm"
            variant="destructive"
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  console.log(data);
  return (
    <DataTable
      className="w-full"
      searchPlaceholder="Pesquise pelo nome da empresa..."
      columns={columns}
      data={data}
      loading={loading}
      initialPageSize={10}
      pageSizeOptions={[10, 25, 50]}
      searchDebounceMs={300}
      enableSearch
      enableSorting
      enablePagination
    />
  );
}
