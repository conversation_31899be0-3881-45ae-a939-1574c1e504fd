import { useSearchParams } from 'react-router-dom';

export default function IframePage() {
  const [searchParams] = useSearchParams();
  const url = searchParams.get('url');

  if (!url) {
    return (
      <div className="flex flex-wrap gap-8 p-8 px-6">
        <h1>URL não informada</h1>
      </div>
    );
  }

  return (
    <div className="flex flex-wrap gap-8 p-8 px-6">
      <iframe
        src={url}
        className="w-full h-full"
      />
    </div>
  );
}
