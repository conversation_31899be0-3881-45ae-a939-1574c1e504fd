/* eslint-disable @typescript-eslint/no-explicit-any */
import { formatters } from '@/utils/formatters';
import { RemoteLoadAvailableDates } from './types';
import { httpClient } from '@/services/httpClient';

export const remoteLoadAvailableDates: RemoteLoadAvailableDates = async (
  params,
) => {
  const today = new Date();
  const startDate = params.dataInicio || today;

  const shouldFetchSixtyDays = params.haveToFetchSixtyDays;

  const firstPeriodEndDate = new Date(startDate);
  firstPeriodEndDate.setDate(firstPeriodEndDate.getDate() + 30);

  // Only calculate second period dates if we need to fetch 60 days
  let secondPeriodStartDate: Date | null = null;
  let secondPeriodEndDate: Date | null = null;

  if (shouldFetchSixtyDays) {
    secondPeriodStartDate = new Date(startDate);
    secondPeriodStartDate.setDate(secondPeriodStartDate.getDate() + 31);

    secondPeriodEndDate = new Date(startDate);
    secondPeriodEndDate.setDate(secondPeriodEndDate.getDate() + 60);
  }

  // Helper function to make a single API request
  const makeApiRequest = async (
    requestStartDate: Date,
    requestEndDate: Date,
  ) => {
    const parametro = {
      empresa: '257308',
      codigo: '159111',
      chave: '3f1525a4308ad1308944',
      tipoSaida: params.tipoSaida || 'json',
      empresaTrabalho: params.codigoSoc,
      dataInicio: requestStartDate.toLocaleDateString('pt-BR'),
      dataFim: requestEndDate.toLocaleDateString('pt-BR'),
      codigoAgenda: params.codigoAgenda,
      statusAgendaFiltro: '1',
    };

    const urlSearchParams = new URLSearchParams({
      parametro: JSON.stringify(parametro),
    });

    return await httpClient.get('/soc/exportardados', {
      params: {
        parametros: `${urlSearchParams.toString()}`,
      },
    });
  };

  try {
    let combinedData: any[] = [];

    if (shouldFetchSixtyDays && secondPeriodStartDate && secondPeriodEndDate) {
      const [firstResponse, secondResponse] = await Promise.all([
        makeApiRequest(startDate, firstPeriodEndDate),
        makeApiRequest(secondPeriodStartDate, secondPeriodEndDate),
      ]);

      combinedData = [
        ...(firstResponse.data || []),
        ...(secondResponse.data || []),
      ];
    } else {
      const response = await makeApiRequest(startDate, firstPeriodEndDate);
      combinedData = response.data || [];
    }

    return {
      statusCode: 200,
      title: 'Pesquisa concluída',
      message: 'Pesquisa concluída com sucesso',
      errorType: null,
      hasError: false,
      result: combinedData.map((item: any) => ({
        ...item,
        dateTyped: formatters.datePTBRToType(item.data),
      })),
    };
  } catch (error) {
    console.error('Error loading available dates:', error);

    return {
      statusCode: 500,
      title: 'Erro na pesquisa',
      message: 'Ocorreu um erro ao buscar as datas disponíveis',
      errorType: 'API_ERROR',
      hasError: true,
      result: [],
    };
  }
};
