import { ReactNode } from 'react';
import { usePageTitle } from '@/hooks/useDocumentTitle';

interface PageWithTitleProps {
  /**
   * Título customizado para a página
   */
  title: string;
  /**
   * Conteúdo da página
   */
  children: ReactNode;
  /**
   * Sufixo customizado (opcional, padrão: "Portal")
   */
  suffix?: string;
  /**
   * Separador customizado (opcional, padrão: " - ")
   */
  separator?: string;
}

/**
 * Componente wrapper que define um título customizado para a página
 * e renderiza o conteúdo filho. Útil para páginas que precisam de
 * títulos específicos que não seguem a convenção automática.
 *
 * @example
 * ```tsx
 * <PageWithTitle title="Relatório Detalhado">
 *   <div>Conteúdo da página...</div>
 * </PageWithTitle>
 * ```
 */
export function PageWithTitle({
  title,
  children,
  suffix = 'Portal',
  separator = ' - ',
}: PageWithTitleProps) {
  usePageTitle(title, { suffix, separator });

  return <>{children}</>;
}
