import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useApi } from '@/contexts/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm } from 'react-hook-form';
import { ChangePasswordFormProps } from './types';
import {
  changePasswordFormSchema,
  ChangePasswordFormValues,
} from './validation';

export default function ChangePasswordForm({
  token,
  onSuccess,
  onError,
}: ChangePasswordFormProps) {
  const { auth } = useApi();
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
  } = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordFormSchema),
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
    },
  });

  const onSubmit: SubmitHandler<ChangePasswordFormValues> = async ({
    newPassword,
  }) => {
    try {
      await auth.changePasswordWithToken({ token, senha: newPassword });
      onSuccess();
    } catch (error) {
      onError();
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col gap-12 w-[360px] items-center"
    >
      <h1 className="text-center text-2xl font-semibold">
        Defina uma nova senha
      </h1>

      <div className="flex flex-col gap-8 w-full">
        <Input
          type="password"
          label="Nova senha"
          id="new-password"
          placeholder="Digite a nova senha"
          errorMessage={errors.newPassword?.message}
          {...register('newPassword')}
        />

        <Input
          type="password"
          label="Confirmação da nova senha"
          id="confirm-password"
          placeholder="Digite a nova senha"
          errorMessage={errors.confirmPassword?.message}
          {...register('confirmPassword')}
        />
      </div>

      <Button
        className="w-full"
        type="submit"
        disabled={
          !!errors.newPassword || !!errors.confirmPassword || isSubmitting
        }
        isLoading={isSubmitting}
      >
        Alterar senha
      </Button>
    </form>
  );
}
