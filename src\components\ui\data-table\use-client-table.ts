import React, { useState, useCallback, useMemo } from 'react';
import { 
  SortConfig,
  PaginationConfig,
  SearchConfig 
} from './types';
import { updateSortConfig } from './sortable-header';
import { useDebounce } from './search';

interface UseClientTableOptions<T = any> {
  data: T[];
  initialPageSize?: number;
  pageSizeOptions?: number[];
  searchDebounceMs?: number;
  multiSort?: boolean;
  searchableFields?: (keyof T)[];
}

export function useClientTable<T = any>({
  data,
  initialPageSize = 10,
  pageSizeOptions = [10, 25, 50, 100],
  searchDebounceMs = 300,
  multiSort = false,
  searchableFields = [],
}: UseClientTableOptions<T>) {
  
  // Table state
  const [pagination, setPagination] = useState<PaginationConfig>({
    page: 1,
    pageSize: initialPageSize,
    total: 0,
  });

  const [sorting, setSorting] = useState<SortConfig[]>([]);
  const [search, setSearch] = useState<SearchConfig>({
    query: '',
    debounceMs: searchDebounceMs,
  });

  // Debounce search query
  const debouncedSearchQuery = useDebounce(search.query, searchDebounceMs);

  // Filter data based on search
  const filteredData = useMemo(() => {
    if (!debouncedSearchQuery.trim()) {
      return data;
    }

    const searchLower = debouncedSearchQuery.toLowerCase();
    
    return data.filter((item) => {
      // If specific searchable fields are defined, search only in those
      if (searchableFields.length > 0) {
        return searchableFields.some(field => {
          const value = item[field];
          return value?.toString().toLowerCase().includes(searchLower);
        });
      }
      
      // Otherwise, search in all string fields
      return Object.values(item as any).some(value => 
        value?.toString().toLowerCase().includes(searchLower)
      );
    });
  }, [data, debouncedSearchQuery, searchableFields]);

  // Sort filtered data
  const sortedData = useMemo(() => {
    if (sorting.length === 0) {
      return filteredData;
    }

    return [...filteredData].sort((a, b) => {
      for (const sort of sorting) {
        const aValue = (a as any)[sort.key];
        const bValue = (b as any)[sort.key];
        
        if (aValue === bValue) continue;
        
        let comparison = 0;
        if (aValue > bValue) comparison = 1;
        if (aValue < bValue) comparison = -1;
        
        if (sort.direction === 'desc') {
          comparison *= -1;
        }
        
        return comparison;
      }
      return 0;
    });
  }, [filteredData, sorting]);

  // Paginate sorted data
  const paginatedData = useMemo(() => {
    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return sortedData.slice(startIndex, endIndex);
  }, [sortedData, pagination.page, pagination.pageSize]);

  // Update total when filtered data changes
  React.useEffect(() => {
    setPagination(prev => ({
      ...prev,
      total: sortedData.length,
    }));
  }, [sortedData.length]);

  // Reset to first page when search or sorting changes
  React.useEffect(() => {
    setPagination(prev => ({ ...prev, page: 1 }));
  }, [debouncedSearchQuery, sorting]);

  // Handlers
  const handlePageChange = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  const handlePageSizeChange = useCallback((pageSize: number) => {
    setPagination(prev => ({ 
      ...prev, 
      pageSize, 
      page: 1 // Reset to first page when changing page size
    }));
  }, []);

  const handleSort = useCallback((sortKey: string) => {
    setSorting(prev => updateSortConfig(prev, sortKey, multiSort));
  }, [multiSort]);

  const handleSearch = useCallback((query: string) => {
    setSearch(prev => ({ ...prev, query }));
  }, []);

  const handleSearchClear = useCallback(() => {
    setSearch(prev => ({ ...prev, query: '' }));
  }, []);

  const handleReset = useCallback(() => {
    setPagination({
      page: 1,
      pageSize: initialPageSize,
      total: 0,
    });
    setSorting([]);
    setSearch({ query: '', debounceMs: searchDebounceMs });
  }, [initialPageSize, searchDebounceMs]);

  // Computed values
  const totalPages = Math.ceil(sortedData.length / pagination.pageSize);
  const hasData = paginatedData.length > 0;
  const isEmpty = !hasData && data.length === 0;
  const isFiltered = debouncedSearchQuery.trim() !== '' || sorting.length > 0;
  const noResultsFromFilter = !hasData && data.length > 0 && isFiltered;

  return {
    // Data
    data: paginatedData,
    allData: data,
    filteredData: sortedData,
    
    // State
    pagination: {
      ...pagination,
      total: sortedData.length,
      pageSizeOptions,
    },
    sorting,
    search,
    
    // Computed values
    totalPages,
    hasData,
    isEmpty,
    isFiltered,
    noResultsFromFilter,
    
    // Handlers
    handlePageChange,
    handlePageSizeChange,
    handleSort,
    handleSearch,
    handleSearchClear,
    handleReset,
  };
}
