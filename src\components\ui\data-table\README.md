# DataTable Component

A comprehensive, reusable table component built with shadcn/ui, Tailwind CSS, and client-side data management. Features client-side pagination, search, sorting, and full accessibility support.

## Features

- ✅ **Client-side data management** - Pass your data directly to the component
- ✅ **Pagination** with numbered controls and page size selector
- ✅ **Search** with debounced input and clear functionality
- ✅ **Sorting** with visual indicators (single or multi-column)
- ✅ **Loading and empty states**
- ✅ **Responsive design** that works on mobile devices
- ✅ **Full accessibility** with ARIA labels and keyboard navigation
- ✅ **TypeScript support** with comprehensive type definitions
- ✅ **Customizable** styling and components

## Installation

The component uses the following dependencies that should already be available in your project:

```bash
# Core dependencies (should already be installed)
@tanstack/react-query
react-hook-form
zod
tailwindcss
lucide-react
```

## Basic Usage

```tsx
import { DataTable, ColumnDef } from '@/components/ui/data-table';
import { useState, useEffect } from 'react';

// Define your data type
interface User {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive';
}

// Define columns
const columns: ColumnDef<User>[] = [
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
    sortable: true,
  },
  {
    id: 'email',
    header: 'Email',
    accessorKey: 'email',
    sortable: true,
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    cell: ({ value }) => (
      <Badge variant={value === 'active' ? 'default' : 'destructive'}>
        {value}
      </Badge>
    ),
  },
];

// Use the component
function UsersTable() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch your data however you prefer (React Query, SWR, fetch, etc.)
  useEffect(() => {
    fetchUsers().then(data => {
      setUsers(data);
      setLoading(false);
    });
  }, []);

  return (
    <DataTable
      columns={columns}
      data={users}
      loading={loading}
      initialPageSize={10}
      enableSearch={true}
      enableSorting={true}
      enablePagination={true}
    />
  );
}
```

## API Reference

### DataTable Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `columns` | `ColumnDef<T>[]` | **Required** | Column definitions |
| `data` | `T[]` | **Required** | Array of data to display |
| `loading` | `boolean` | `false` | Show loading state |
| `initialPageSize` | `number` | `10` | Initial page size |
| `pageSizeOptions` | `number[]` | `[10, 25, 50, 100]` | Available page sizes |
| `enableSearch` | `boolean` | `true` | Enable search functionality |
| `enableSorting` | `boolean` | `true` | Enable sorting functionality |
| `enablePagination` | `boolean` | `true` | Enable pagination |
| `multiSort` | `boolean` | `false` | Allow multi-column sorting |
| `searchPlaceholder` | `string` | `"Search..."` | Search input placeholder |
| `searchDebounceMs` | `number` | `300` | Search debounce delay |
| `onRowClick` | `(row: T) => void` | `undefined` | Row click handler |
| `ariaLabel` | `string` | `"Data table"` | ARIA label for table |

### ColumnDef Interface

```tsx
interface ColumnDef<T> {
  id: string;                           // Unique column identifier
  header: string | ReactNode;           // Column header content
  accessorKey?: keyof T;                // Object key to access data
  accessorFn?: (row: T) => any;         // Custom accessor function
  cell?: (props: { row: T; value: any }) => ReactNode; // Custom cell renderer
  sortable?: boolean;                   // Enable sorting (default: true)
  searchable?: boolean;                 // Include in search (default: true)
  align?: 'left' | 'center' | 'right';  // Text alignment
  width?: string | number;              // Column width
  className?: string;                   // Cell CSS classes
  headerClassName?: string;             // Header CSS classes
}
```

### Data Fetching Function

Your `fetchData` function should match this signature:

```tsx
type DataFetchFn<T> = (params: TableApiParams) => Promise<TableApiResponse<T>>;

interface TableApiParams {
  page: number;
  pageSize: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  [key: string]: any; // Additional filters
}

interface TableApiResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}
```

## Advanced Usage

### Custom Cell Renderers

```tsx
const columns: ColumnDef<User>[] = [
  {
    id: 'avatar',
    header: 'Avatar',
    cell: ({ row }) => (
      <img 
        src={row.avatarUrl} 
        alt={row.name}
        className="w-8 h-8 rounded-full"
      />
    ),
  },
  {
    id: 'actions',
    header: 'Actions',
    sortable: false,
    cell: ({ row }) => (
      <div className="flex space-x-2">
        <Button size="sm" onClick={() => editUser(row.id)}>
          Edit
        </Button>
        <Button size="sm" variant="destructive" onClick={() => deleteUser(row.id)}>
          Delete
        </Button>
      </div>
    ),
  },
];
```

### Custom Components

```tsx
<DataTable
  columns={columns}
  fetchData={fetchUsers}
  loadingComponent={<CustomLoader />}
  errorComponent={(error) => <CustomError message={error} />}
  emptyComponent={<CustomEmptyState />}
  rowClassName={(row) => row.isVip ? 'bg-yellow-50' : ''}
/>
```

### Row Click Handling

```tsx
<DataTable
  columns={columns}
  fetchData={fetchUsers}
  onRowClick={(user) => {
    navigate(`/users/${user.id}`);
  }}
/>
```

## Accessibility Features

- **ARIA labels** and descriptions for screen readers
- **Keyboard navigation** support (Tab, Enter, Space)
- **Screen reader announcements** for loading states and data changes
- **Proper table semantics** with role attributes
- **Focus management** for interactive elements

## Styling and Customization

The component uses Tailwind CSS classes and can be customized through:

- `className` props for different sections
- CSS custom properties for theming
- Custom component overrides
- Tailwind utility classes

## Examples

See `example.tsx` for comprehensive usage examples including:

- Basic table setup
- Custom cell renderers
- Row click handling
- Custom loading/error/empty states
- Advanced styling options

## TypeScript Support

The component is fully typed with TypeScript, providing:

- Type-safe column definitions
- Inferred data types from your API
- Comprehensive prop validation
- IDE autocomplete support
