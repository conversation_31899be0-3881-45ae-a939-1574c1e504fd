import { useState } from 'react';
import { useParams } from 'react-router-dom';
import PasswordChangedSuccessfully from './components/changedSuccessfully';
import ChangePasswordForm from './components/ChangePasswordForm';
import ExpiredTokenError from './components/tokenError';
import { usePageTitle } from '@/hooks';

export default function ChangePasswordPage() {
  usePageTitle('Alterar Senha');
  const { token } = useParams();
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const renderContent = () => {
    if (status === 'error') return <ExpiredTokenError />;
    if (status === 'success') return <PasswordChangedSuccessfully />;
    return (
      <ChangePasswordForm
        token={token as string}
        onSuccess={() => setStatus('success')}
        onError={() => setStatus('error')}
      />
    );
  };

  return (
    <div className="h-screen w-full flex">
      <div className="flex flex-1 justify-center items-center">
        {renderContent()}
      </div>

      <div className="hidden md:flex h-screen flex-1 w-full items-center justify-center bg-gradient-to-b from-teal-300/70 to-primary">
        <div>
          <img
            src="/imgs/bencorpPortalCliente.png"
            alt="Logo da Onyma por Bencorp"
          />
        </div>
      </div>
    </div>
  );
}
